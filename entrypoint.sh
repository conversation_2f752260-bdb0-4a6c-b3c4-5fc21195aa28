#!/bin/bash
set -e

echo "🚀 Starting Mobile MCP Server with <PERSON>lutter and Android SDK..."
echo "============================================================"

# Check mobile development environment
echo "📱 Checking mobile development environment..."
echo "Node.js version: $(node --version)"
echo "Java version: $(java -version 2>&1 | head -n 1)"
echo "Android SDK: $ANDROID_HOME"
echo "Flutter SDK: $FLUTTER_HOME"

# Check if Flutter is available
if command -v flutter >/dev/null 2>&1; then
    echo "✅ Flutter version: $(flutter --version | head -n 1)"
    # Run flutter doctor to check setup
    echo "🔍 Running Flutter doctor..."
    flutter doctor --android-licenses >/dev/null 2>&1 || true
    flutter doctor
else
    echo "❌ Flutter not found in PATH"
fi

# Check if ADB is available
if command -v adb >/dev/null 2>&1; then
    echo "✅ ADB version: $(adb version | head -n 1)"
    
    # Try to connect to host ADB server
    echo "🔌 Attempting to connect to host ADB server..."
    adb connect host.docker.internal:5037 2>/dev/null || echo "⚠️  Could not connect to host ADB server"
    
    # List devices
    echo "📱 Available devices:"
    adb devices
else
    echo "❌ ADB not found in PATH"
fi

echo ""
echo "🚀 Starting MCP Mobile Server..."

# Start the MCP server
exec mcp-server-mobile "$@"
