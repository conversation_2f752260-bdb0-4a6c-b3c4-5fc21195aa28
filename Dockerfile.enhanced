﻿# Enhanced Dockerfile for mobile-mcp with <PERSON>lutter and Android SDK
# Use Ubuntu as base for better Android SDK support
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies for Node.js, Android SDK, and Flutter
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    zip \
    git \
    build-essential \
    file \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    openjdk-17-jdk \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 18 LTS
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Set up environment variables for Android and Flutter
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV ANDROID_HOME=/opt/android-sdk
ENV ANDROID_SDK_ROOT=/opt/android-sdk
ENV FLUTTER_HOME=/opt/flutter
ENV PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools:$FLUTTER_HOME/bin

# Create directories for Android SDK and Flutter
RUN mkdir -p $ANDROID_HOME $FLUTTER_HOME

# Install Android SDK Command Line Tools
RUN cd /opt && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip && \
    unzip commandlinetools-linux-11076708_latest.zip -d $ANDROID_HOME && \
    rm commandlinetools-linux-11076708_latest.zip && \
    mv $ANDROID_HOME/cmdline-tools $ANDROID_HOME/cmdline-tools-temp && \
    mkdir -p $ANDROID_HOME/cmdline-tools/latest && \
    mv $ANDROID_HOME/cmdline-tools-temp/* $ANDROID_HOME/cmdline-tools/latest/ && \
    rmdir $ANDROID_HOME/cmdline-tools-temp

# Accept Android SDK licenses and install required packages
RUN yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses && \
    $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager \
        "platform-tools" \
        "platforms;android-36" \
        "build-tools;36.0.0" \
        "cmdline-tools;latest"

# Install Flutter SDK
RUN cd /opt && \
    git clone https://github.com/flutter/flutter.git -b stable --depth 1 && \
    flutter doctor --android-licenses && \
    flutter config --android-sdk $ANDROID_HOME

# Set working directory
WORKDIR /app

# Install the published mobile-mcp package globally
RUN npm install -g @mobilenext/mobile-mcp@latest

# Create a non-root user for security
RUN groupadd -g 1001 nodejs
RUN useradd -u 1001 -g nodejs -m mobile-mcp

# Set proper permissions for Android SDK and Flutter
RUN chown -R mobile-mcp:nodejs $ANDROID_HOME $FLUTTER_HOME

# Create app directory and set ownership
RUN mkdir -p /app && chown -R mobile-mcp:nodejs /app
USER mobile-mcp

# Set working directory for the user
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV PATH=/usr/local/bin:$PATH
ENV ADB_SERVER_SOCKET=tcp:host.docker.internal:5037
ENV ANDROID_ADB_SERVER_PORT=5037

# Health check to ensure the server can start
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "console.log('Health check passed')" || exit 1

# Create entrypoint script
COPY --chown=mobile-mcp:nodejs entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Run the MCP server using the globally installed package
ENTRYPOINT ["/app/entrypoint.sh"]
