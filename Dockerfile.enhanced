﻿# Enhanced Dockerfile for mobile-mcp using published npm package
# Use Node.js 18 LTS as base image with additional system dependencies
FROM node:18-alpine

# Install system dependencies that might be needed for mobile automation
RUN apk add --no-cache \
    python3 \
    py3-pip \
    openjdk11-jre-headless \
    curl \
    unzip \
    bash \
    git

# Set working directory
WORKDIR /app

# Install the published mobile-mcp package globally
RUN npm install -g @mobilenext/mobile-mcp@latest

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S mobile-mcp -u 1001

# Create app directory and set ownership
RUN mkdir -p /app && chown -R mobile-mcp:nodejs /app
USER mobile-mcp

# Set working directory for the user
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV PATH=/usr/local/bin:$PATH

# Health check to ensure the server can start
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "console.log('Health check passed')" || exit 1

# Run the MCP server using the globally installed package
ENTRYPOINT ["mcp-server-mobile"]
