<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="robots" content="noindex,nofollow">
    <meta name="application-name" content="FireAlerts911">
    <title>FireAlerts911 - Reset Password</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* Override auth container styles to match login exactly */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: var(--primary-dark);
            box-sizing: border-box;
            padding: 0;
        }
        
        /* Make sure auth card has exact same dimensions */
        .auth-card {
            width: 400px;
            background-color: var(--secondary-dark);
            border-radius: 5px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin: 0 auto;
        }
        
        /* Notification styling */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
            max-width: 350px;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
            transform: translateX(120%);
            transition: transform 0.3s ease-in-out;
            z-index: 1000;
            display: flex;
            align-items: center;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background-color: #4caf50;
        }
        
        .notification.error {
            background-color: #f44336;
        }
        
        .notification.info {
            background-color: #2196f3;
        }
        
        .notification.warning {
            background-color: #ff9800;
        }
        
        .notification i {
            margin-right: 10px;
        }
        
        /* Form validation styling */
        .form-control.error {
            border-color: #f44336;
        }
        
        .error-message {
            color: #f44336;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        .error-message.show {
            display: block;
        }

        /* Password requirements styling */
        .password-requirements {
            background-color: rgba(30, 136, 229, 0.1);
            border: 1px solid rgba(30, 136, 229, 0.3);
            border-radius: 4px;
            padding: 12px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .password-requirements h4 {
            margin: 0 0 8px 0;
            color: #1e88e5;
            font-size: 13px;
        }
        
        .requirement {
            display: flex;
            align-items: center;
            margin: 4px 0;
            color: var(--text-secondary);
        }
        
        .requirement i {
            margin-right: 8px;
            width: 12px;
            font-size: 10px;
        }
        
        .requirement.valid {
            color: #4caf50;
        }
        
        .requirement.invalid {
            color: #f44336;
        }

        /* Success message styling */
        .success-message {
            color: #4caf50;
            font-size: 14px;
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4caf50;
            display: none;
        }
        
        .success-message.show {
            display: block;
        }

        /* Token error styling */
        .token-error {
            color: #f44336;
            font-size: 14px;
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 4px;
            background-color: rgba(244, 67, 54, 0.1);
            border-left: 3px solid #f44336;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <i class="fas fa-fire-alt fa-2x" style="color: #e53935; margin-right: 10px;"></i>
                    <h1 style="margin: 0; font-size: 24px;">FireAlerts911</h1>
                </div>
                <h2 style="margin: 0; font-size: 18px; font-weight: 400;">Set New Password</h2>
                <p style="margin: 10px 0 0 0; font-size: 14px; color: var(--text-secondary);">Enter your new password below.</p>
            </div>
            <div class="auth-body">
                <!-- Token Error Message -->
                <div class="token-error" id="token-error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div id="token-error-message">Invalid or expired reset link.</div>
                </div>

                <form id="resetPasswordForm" action="javascript:void(0);" method="post" autocomplete="on" style="display: none;">
                    <div class="form-group">
                        <label class="form-label" for="password">New Password</label>
                        <div style="position: relative;">
                            <input type="password" class="form-control" id="password" name="password" required autocomplete="new-password" style="padding-left: 40px;" placeholder="Enter your new password">
                            <i class="fas fa-lock" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: var(--text-secondary);"></i>
                        </div>
                        <div class="error-message" id="password-error">Password is required</div>
                        
                        <!-- Password Requirements -->
                        <div class="password-requirements">
                            <h4>Password Requirements:</h4>
                            <div class="requirement" id="req-length">
                                <i class="fas fa-times"></i>
                                <span>At least 8 characters</span>
                            </div>
                            <div class="requirement" id="req-uppercase">
                                <i class="fas fa-times"></i>
                                <span>One uppercase letter (A-Z)</span>
                            </div>
                            <div class="requirement" id="req-lowercase">
                                <i class="fas fa-times"></i>
                                <span>One lowercase letter (a-z)</span>
                            </div>
                            <div class="requirement" id="req-number">
                                <i class="fas fa-times"></i>
                                <span>One number (0-9)</span>
                            </div>
                            <div class="requirement" id="req-special">
                                <i class="fas fa-times"></i>
                                <span>One special character (!@#$%^&*)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="confirmPassword">Confirm New Password</label>
                        <div style="position: relative;">
                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required autocomplete="new-password" style="padding-left: 40px;" placeholder="Confirm your new password">
                            <i class="fas fa-lock" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: var(--text-secondary);"></i>
                        </div>
                        <div class="error-message" id="confirm-password-error">Passwords do not match</div>
                    </div>
                    
                    <!-- Success Message Container -->
                    <div class="success-message" id="success-message">
                        <i class="fas fa-check-circle"></i> Password reset successfully! Redirecting to login...
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" id="resetButton" style="width: 100%;">
                            <i class="fas fa-key"></i> Reset Password
                        </button>
                    </div>
                </form>
            </div>
            <div class="auth-footer">
                <a href="login.html" style="color: var(--accent-blue); text-decoration: none;">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            </div>
        </div>
    </div>
    
    <!-- Notification element -->
    <div class="notification" id="notification">
        <i class="fas fa-info-circle"></i>
        <span id="notification-message"></span>
    </div>
    
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Get reset token from URL
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get("token");
            
            // Check if token exists
            if (!token) {
                showTokenError("No reset token provided. Please request a new password reset.");
                return;
            }
            
            // Validate token with server
            validateResetToken(token);
            
            function showTokenError(message) {
                document.getElementById("token-error-message").textContent = message;
                document.getElementById("token-error").style.display = "block";
                document.getElementById("resetPasswordForm").style.display = "none";
            }
            
            function validateResetToken(token) {
                // Call API to validate token
                fetch(`${API_BASE_URL}/auth/validate-reset-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: token }),
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Token is valid, show the form
                        document.getElementById("resetPasswordForm").style.display = "block";
                        document.getElementById("token-error").style.display = "none";
                    } else {
                        showTokenError(data.message || "Invalid or expired reset token. Please request a new password reset.");
                    }
                })
                .catch(error => {
                    console.error("Token validation error:", error);
                    showTokenError("Unable to validate reset token. Please try again or request a new password reset.");
                });
            }

            // Local notification function
            function showLocalNotification(message, type = "info") {
                const notification = document.getElementById("notification");
                const notificationMessage = document.getElementById("notification-message");

                // Remove existing classes
                notification.className = "notification";
                notification.classList.add(type);

                // Set message
                notificationMessage.textContent = message;

                // Show notification
                notification.classList.add("show");

                // Hide after 5 seconds
                setTimeout(() => {
                    notification.classList.remove("show");
                }, 5000);
            }

            // Make function available globally for this page
            window.showLocalNotification = showLocalNotification;

            // Password validation requirements
            const passwordRequirements = {
                length: { regex: /.{8,}/, element: 'req-length' },
                uppercase: { regex: /[A-Z]/, element: 'req-uppercase' },
                lowercase: { regex: /[a-z]/, element: 'req-lowercase' },
                number: { regex: /[0-9]/, element: 'req-number' },
                special: { regex: /[!@#$%^&*]/, element: 'req-special' }
            };

            // Password validation function
            function validatePassword(password) {
                let isValid = true;

                for (const [key, requirement] of Object.entries(passwordRequirements)) {
                    const element = document.getElementById(requirement.element);
                    const icon = element.querySelector('i');

                    if (requirement.regex.test(password)) {
                        element.classList.remove('invalid');
                        element.classList.add('valid');
                        icon.className = 'fas fa-check';
                    } else {
                        element.classList.remove('valid');
                        element.classList.add('invalid');
                        icon.className = 'fas fa-times';
                        isValid = false;
                    }
                }

                return isValid;
            }

            // Form validation function
            function validateForm() {
                const password = document.getElementById("password");
                const confirmPassword = document.getElementById("confirmPassword");
                let isValid = true;

                // Validate password requirements
                if (!password.value) {
                    password.classList.add("error");
                    document.getElementById("password-error").textContent = "Password is required";
                    document.getElementById("password-error").classList.add("show");
                    isValid = false;
                } else if (!validatePassword(password.value)) {
                    password.classList.add("error");
                    document.getElementById("password-error").textContent = "Password does not meet requirements";
                    document.getElementById("password-error").classList.add("show");
                    isValid = false;
                } else {
                    password.classList.remove("error");
                    document.getElementById("password-error").classList.remove("show");
                }

                // Validate password confirmation
                if (!confirmPassword.value) {
                    confirmPassword.classList.add("error");
                    document.getElementById("confirm-password-error").textContent = "Please confirm your password";
                    document.getElementById("confirm-password-error").classList.add("show");
                    isValid = false;
                } else if (password.value !== confirmPassword.value) {
                    confirmPassword.classList.add("error");
                    document.getElementById("confirm-password-error").textContent = "Passwords do not match";
                    document.getElementById("confirm-password-error").classList.add("show");
                    isValid = false;
                } else {
                    confirmPassword.classList.remove("error");
                    document.getElementById("confirm-password-error").classList.remove("show");
                }

                return isValid;
            }

            // Add input event listeners for real-time validation
            document.getElementById("password").addEventListener("input", function() {
                this.classList.remove("error");
                document.getElementById("password-error").classList.remove("show");
                document.getElementById("success-message").classList.remove("show");

                // Real-time password validation
                if (this.value) {
                    validatePassword(this.value);
                }
            });

            document.getElementById("confirmPassword").addEventListener("input", function() {
                this.classList.remove("error");
                document.getElementById("confirm-password-error").classList.remove("show");
                document.getElementById("success-message").classList.remove("show");

                // Check if passwords match in real-time
                const password = document.getElementById("password").value;
                if (this.value && password) {
                    const confirmError = document.getElementById("confirm-password-error");
                    if (password === this.value) {
                        this.classList.remove("error");
                        confirmError.classList.remove("show");
                    } else {
                        this.classList.add("error");
                        confirmError.textContent = "Passwords do not match";
                        confirmError.classList.add("show");
                    }
                }
            });

            // Form submission handler
            document.getElementById("resetPasswordForm").addEventListener("submit", function(e) {
                e.preventDefault();

                // Validate form
                if (!validateForm()) {
                    return;
                }

                // Hide any previous success messages
                document.getElementById("success-message").classList.remove("show");

                const password = this.elements.password.value;

                // Show loading state
                const submitButton = document.getElementById("resetButton");
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Resetting...";
                submitButton.disabled = true;

                // Call the API reset password method
                fetch(`${API_BASE_URL}/auth/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: token,
                        password: password
                    }),
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message in form
                        document.getElementById("success-message").classList.add("show");

                        // Also show notification
                        if (typeof window.showNotification === "function") {
                            window.showNotification("Password reset successfully! Redirecting to login...", "success");
                        } else {
                            showLocalNotification("Password reset successfully! Redirecting to login...", "success");
                        }

                        // Clear the form
                        this.reset();

                        // Reset password requirements display
                        for (const requirement of Object.values(passwordRequirements)) {
                            const element = document.getElementById(requirement.element);
                            const icon = element.querySelector('i');
                            element.classList.remove('valid', 'invalid');
                            icon.className = 'fas fa-times';
                        }

                        // Redirect to login after a delay
                        setTimeout(() => {
                            window.location.href = "login.html?message=password_reset_success";
                        }, 3000);
                    } else {
                        // Show error message
                        const errorMessage = data.message || "Failed to reset password. Please try again.";
                        if (typeof window.showNotification === "function") {
                            window.showNotification(errorMessage, "error");
                        } else {
                            showLocalNotification(errorMessage, "error");
                        }
                    }
                })
                .catch(error => {
                    console.error("Reset password error:", error);

                    // Show error notification
                    if (typeof window.showNotification === "function") {
                        window.showNotification("An error occurred while resetting your password. Please try again.", "error");
                    } else {
                        showLocalNotification("An error occurred while resetting your password. Please try again.", "error");
                    }
                })
                .finally(() => {
                    // Reset button state
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                });
            });
        });
    </script>
</body>
</html>
