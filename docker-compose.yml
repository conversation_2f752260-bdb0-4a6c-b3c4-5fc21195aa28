services:
  mobile-mcp:
    build:
      context: .
      dockerfile: Dockerfile.enhanced
    container_name: mobile-mcp-server
    restart: unless-stopped
    stdin_open: true
    tty: true
    
    # Environment variables (if needed)
    environment:
      - NODE_ENV=production
      - DEBUG=mobile-mcp:*
    
    # Volume mounts for mobile automation tools and data persistence
    volumes:
      # Mount for Android SDK (if available on host)
      # - /usr/local/android-sdk:/opt/android-sdk:ro
      # Mount for data persistence
      - mobile-mcp-data:/app/data
      # Mount for logs
      - mobile-mcp-logs:/app/logs
    
    # Network configuration for device communication
    network_mode: "host"
    
    # Health check
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mobile-mcp-data:
  mobile-mcp-logs:
