services:
  mobile-mcp:
    build:
      context: .
      dockerfile: Dockerfile.enhanced
    container_name: mobile-mcp-server
    restart: unless-stopped
    stdin_open: true
    tty: true
    
    # Environment variables for mobile development
    environment:
      - NODE_ENV=production
      - DEBUG=mobile-mcp:*
      - JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
      - ANDROID_HOME=/opt/android-sdk
      - ANDROID_SDK_ROOT=/opt/android-sdk
      - FLUTTER_HOME=/opt/flutter
      - ADB_SERVER_SOCKET=tcp:host.docker.internal:5037
      - ANDROID_ADB_SERVER_PORT=5037
    
    # Volume mounts for mobile automation tools and data persistence
    volumes:
      # Mount for Android SDK (if available on host)
      # - /usr/local/android-sdk:/opt/android-sdk:ro
      # Mount for data persistence
      - mobile-mcp-data:/app/data
      # Mount for logs
      - mobile-mcp-logs:/app/logs
    
    # Network configuration for device communication
    network_mode: "bridge"

    # Port mapping for ADB server communication
    ports:
      - "5037:5037"  # ADB server port

    # Enable privileged mode for USB device access
    privileged: true

    # Device access for USB debugging
    devices:
      - /dev/bus/usb:/dev/bus/usb
    
    # Health check
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mobile-mcp-data:
  mobile-mcp-logs:
