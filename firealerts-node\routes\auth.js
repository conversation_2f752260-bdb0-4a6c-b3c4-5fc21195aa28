const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { check, validationResult } = require('express-validator');
const db = require('../models');
const authMiddleware = require('../middleware/auth');

// Cookie configuration
const cookieConfig = {
  httpOnly: true,                   // Not accessible via JavaScript
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'lax',                  // Lax CSRF protection - allows cross-tab sharing
  maxAge: 24 * 60 * 60 * 1000,      // 24 hours
  path: '/'                         // Available for entire domain, no explicit domain for localhost
};

// @route   POST api/auth/login
// @desc    Authenticate user & get token
// @access  Public
router.post(
  '/login',
  [
    check('username', 'Username is required').exists(),
    check('password', 'Password is required').exists()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, password } = req.body;

    try {
      // Find user by username
      const user = await db.user.findOne({
        where: { username }
      });

      if (!user) {
        return res.status(400).json({ msg: 'Invalid credentials' });
      }

      // Check if user is active
      if (!user.status) {
        return res.status(400).json({ msg: 'Account has been deactivated' });
      }

      // Check password
      const isMatch = await bcrypt.compare(password, user.password);

      if (!isMatch) {

        // Log failed login attempt
        await db.activity.create({
          action: 'failed_login',
          details: JSON.stringify({ username }),
          userId: null,
          ip: req.ip || req.connection.remoteAddress,
          module: 'auth',
          severity: 'warning'
        });

        return res.status(400).json({ msg: 'Invalid credentials' });
      }

      // Get JWT secret from environment or throw error if not set
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        console.error('JWT_SECRET is not set in environment variables');
        return res.status(500).json({ msg: 'Server configuration error' });
      }

      // Create JWT payload with minimal data
      const payload = {
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      };

      // Sign token
      jwt.sign(
        payload,
        jwtSecret,
        { expiresIn: '24h' },
        (err, token) => {
          if (err) throw err;

          // Set the token as an HTTP-only cookie
          res.cookie('authToken', token, cookieConfig);

          // Log successful login
          db.activity.create({
            action: 'login',
            details: JSON.stringify({ username, role: user.role }),
            userId: user.id,
            ip: req.ip || req.connection.remoteAddress,
            module: 'auth',
            severity: 'info'
          }).catch(err => {
            console.error('Error logging login activity:', err);
          });

          // Return token and minimal user info in response body for API usage
          res.json({
            token,
            user: {
              id: user.id,
              username: user.username,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              role: user.role
            }
          });
        }
      );
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   POST api/auth/logout
// @desc    Log out user by clearing cookies
// @access  Public
router.post('/logout', (req, res) => {
  // Get token from header or cookie
  const token = req.header('Authorization')?.replace('Bearer ', '') ||
               (req.cookies && req.cookies.authToken);

  // Log the logout if we have user info
  if (token) {
    try {
      // Try to decode the token to get user info for logging
      const jwtSecret = process.env.JWT_SECRET;
      if (jwtSecret) {
        const decoded = jwt.verify(token, jwtSecret);
        if (decoded && decoded.user) {
          // Log logout activity
          db.activity.create({
            action: 'logout',
            details: JSON.stringify({ username: decoded.user.username }),
            userId: decoded.user.id,
            ip: req.ip || req.connection.remoteAddress,
            module: 'auth',
            severity: 'info'
          }).catch(err => {
            console.error('Error logging logout activity:', err);
          });
        }
      }
    } catch (err) {
      // Just log the error but continue with logout
      console.error('Error logging logout:', err);
    }
  }

  // Clear the authentication cookie with proper configuration
  res.clearCookie('authToken', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  });
  res.json({ msg: 'Logged out successfully' });
});

// @route   GET api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', authMiddleware, async (req, res) => {
  // The auth middleware will attach the user to the request
  try {
    const user = await db.user.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/auth/validate
// @desc    Validate token - useful for client-side auth checks
// @access  Private
router.get('/validate', authMiddleware, (req, res) => {
  // Since we're using the enhanced authMiddleware, it already:
  // 1. Verifies the token
  // 2. Checks that the user exists in the database
  // 3. Verifies their role matches what's in the token
  // 4. Ensures the account is active

  // At this point, req.user contains the latest user data from the database
  res.json({
    valid: true,
    user: req.user
  });
});

// @route   GET api/auth/check-cookie
// @desc    Check if auth cookie exists without full validation - for cross-tab session detection
// @access  Public
router.get('/check-cookie', (req, res) => {
  // Simply check if the auth cookie exists
  const hasCookie = !!(req.cookies && req.cookies.authToken);

  res.json({
    hasCookie: hasCookie
  });
});

// @route   POST api/auth/forgot-password
// @desc    Send password reset email with secure token
// @access  Public
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email address is required'
      });
    }

    // Find user by email
    const user = await db.user.findOne({
      where: { email: email.toLowerCase().trim() }
    });

    // Always return success to prevent email enumeration
    // But only send email if user exists
    if (user) {
      try {
        // Generate a secure reset token
        const resetToken = crypto.randomBytes(32).toString('hex');

        // Set token expiration (1 hour from now)
        const tokenExpires = new Date();
        tokenExpires.setHours(tokenExpires.getHours() + 1);

        // Update user with reset token and expiration
        await user.update({
          passwordResetToken: resetToken,
          passwordResetExpires: tokenExpires
        });

        // Create reset URL
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:80'}/reset-password.html?token=${resetToken}`;

        // Send password reset email with secure link
        const emailService = require('../services/emailService');
        await emailService.sendPasswordResetEmail({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email
        }, resetUrl);

        // Log the activity
        await db.activity.create({
          action: 'password_reset_requested',
          details: JSON.stringify({
            email: email,
            userId: user.id,
            method: 'forgot_password_form',
            tokenExpires: tokenExpires.toISOString()
          }),
          userId: user.id,
          ip: req.ip || req.connection.remoteAddress,
          module: 'auth',
          severity: 'info'
        });

        console.log(`Password reset email sent to ${email} with token expiring at ${tokenExpires}`);
      } catch (emailErr) {
        console.error('Error sending password reset email:', emailErr);
        // Don't fail the request if email fails, but log it
      }
    } else {
      // Log failed attempt for security monitoring
      await db.activity.create({
        action: 'password_reset_failed',
        details: JSON.stringify({
          email: email,
          reason: 'user_not_found'
        }),
        userId: null,
        ip: req.ip || req.connection.remoteAddress,
        module: 'auth',
        severity: 'warning'
      });
    }

    // Always return success message to prevent email enumeration
    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });

  } catch (err) {
    console.error('Forgot password error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error processing password reset request'
    });
  }
});

// @route   POST api/auth/validate-reset-token
// @desc    Validate password reset token
// @access  Public
router.post('/validate-reset-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Reset token is required'
      });
    }

    // Find user with this reset token
    const user = await db.user.findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          [db.Sequelize.Op.gt]: new Date() // Token must not be expired
        }
      }
    });

    if (!user) {
      return res.json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Token is valid
    res.json({
      success: true,
      message: 'Reset token is valid'
    });

  } catch (err) {
    console.error('Token validation error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error validating reset token'
    });
  }
});

// @route   POST api/auth/reset-password
// @desc    Reset password using secure token
// @access  Public
router.post('/reset-password', [
  check('token', 'Reset token is required').exists(),
  check('password', 'Password must be at least 8 characters')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (!@#$%^&*)')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: errors.array()[0].msg,
        errors: errors.array()
      });
    }

    const { token, password } = req.body;

    // Find user with this reset token
    const user = await db.user.findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          [db.Sequelize.Op.gt]: new Date() // Token must not be expired
        }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user password and clear reset token
    await user.update({
      password: hashedPassword,
      passwordResetToken: null,
      passwordResetExpires: null
    });

    // Log the activity
    await db.activity.create({
      action: 'password_reset_completed',
      details: JSON.stringify({
        userId: user.id,
        email: user.email,
        method: 'secure_token_reset'
      }),
      userId: user.id,
      ip: req.ip || req.connection.remoteAddress,
      module: 'auth',
      severity: 'info'
    });

    console.log(`Password successfully reset for user ${user.email}`);

    res.json({
      success: true,
      message: 'Password has been reset successfully'
    });

  } catch (err) {
    console.error('Password reset error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error resetting password'
    });
  }
});

module.exports = router;
