{"buildFiles": ["G:\\GitHub\\vsCodeTool\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\GitHub\\faApp\\test_app\\build\\.cxx\\Debug\\175fq6ui\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\GitHub\\faApp\\test_app\\build\\.cxx\\Debug\\175fq6ui\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}