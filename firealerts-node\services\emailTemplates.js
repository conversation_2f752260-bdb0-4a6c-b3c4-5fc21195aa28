/**
 * Email Templates
 *
 * Centralized email template management for different email types
 * Organized by category: Authentication, Notifications, System, Administrative
 */

const emailTemplates = {

    // ==========================================
    // AUTHENTICATION EMAILS
    // ==========================================

    /**
     * Welcome Email Template
     */
    welcome: {
        generate(user, temporaryPassword = null) {
            const hasPassword = !!temporaryPassword;

            return {
                subject: 'Welcome to FireAlerts911',
                text: this.getTextContent(user, temporaryPassword),
                html: this.getHtmlContent(user, temporaryPassword)
            };
        },

        getTextContent(user, temporaryPassword) {
            const name = `${user.firstName} ${user.lastName}`.trim() || user.email;
            let content = `Welcome to FireAlerts911, ${name}!\n\n`;
            content += `Your account has been created successfully.\n\n`;
            content += `Login Details:\n`;
            content += `Email: ${user.email}\n`;

            if (temporaryPassword) {
                content += `Temporary Password: ${temporaryPassword}\n\n`;
                content += `Important: Please log in and change your password immediately for security.\n\n`;
            }

            content += `You can access your account at: ${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}\n\n`;
            content += `If you have any questions, please contact support.\n\n`;
            content += `Best regards,\nThe FireAlerts911 Team`;

            return content;
        },

        getHtmlContent(user, temporaryPassword) {
            const name = `${user.firstName} ${user.lastName}`.trim() || user.email;
            const hasPassword = !!temporaryPassword;

            return `
                <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #e53935; margin: 0;">FireAlerts911</h1>
                        <p style="color: #666; margin: 5px 0 0 0;">Emergency Alert System</p>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #e53935;">
                        <h2 style="color: #333; margin-top: 0;">Welcome, ${name}!</h2>
                        <p style="font-size: 16px; color: #333; line-height: 1.5;">
                            Your FireAlerts911 account has been created successfully. You're now part of our emergency alert network.
                        </p>
                    </div>

                    <div style="margin: 30px 0;">
                        <h3 style="color: #333; border-bottom: 2px solid #e53935; padding-bottom: 10px;">Login Information</h3>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 10px 0; font-weight: bold; color: #555;">Email:</td>
                                <td style="padding: 10px 0; color: #333;">${user.email}</td>
                            </tr>
                            ${hasPassword ? `
                            <tr>
                                <td style="padding: 10px 0; font-weight: bold; color: #555;">Temporary Password:</td>
                                <td style="padding: 10px 0; color: #333; font-family: monospace; background: #f0f0f0; padding: 5px;">${temporaryPassword}</td>
                            </tr>
                            ` : ''}
                        </table>

                        ${hasPassword ? `
                        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7; margin: 20px 0;">
                            <strong style="color: #856404;">⚠️ Security Notice:</strong>
                            <p style="margin: 5px 0 0 0; color: #856404;">
                                Please log in and change your temporary password immediately for your account security.
                            </p>
                        </div>
                        ` : ''}
                    </div>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}"
                           style="background: #e53935; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            Access Your Account
                        </a>
                    </div>

                    <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
                        <p>If you need assistance, please contact our support team.</p>
                        <p style="margin: 0;">© ${new Date().getFullYear()} FireAlerts911. All rights reserved.</p>
                    </div>
                </div>
            `;
        }
    },

    /**
     * Password Reset Email Template
     */
    passwordReset: {
        generate(user, resetUrl) {
            return {
                subject: 'Password Reset - FireAlerts911',
                text: this.getTextContent(user, resetUrl),
                html: this.getHtmlContent(user, resetUrl)
            };
        },

        getTextContent(user, resetUrl) {
            const name = `${user.firstName} ${user.lastName}`.trim() || user.email;
            return `
Hello ${name},

You have requested a password reset for your FireAlerts911 account.

To reset your password, please click the following link (valid for 1 hour):

${resetUrl}

If the link doesn't work, copy and paste it into your browser's address bar.

If you did not request this password reset, please ignore this email or contact support if you have concerns.

For security reasons, this link will expire in 1 hour.

Best regards,
The FireAlerts911 Team
            `.trim();
        },

        getHtmlContent(user, resetUrl) {
            const name = `${user.firstName} ${user.lastName}`.trim() || user.email;

            return `
                <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
                    <div style="background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <!-- Header -->
                        <div style="background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%); padding: 30px; text-align: center; color: white;">
                            <h1 style="margin: 0; font-size: 28px; font-weight: bold;">🔥 FireAlerts911</h1>
                            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Password Reset Request</p>
                        </div>

                        <!-- Content -->
                        <div style="padding: 30px;">
                            <h2 style="color: #333; margin-top: 0; font-size: 24px;">Reset Your Password</h2>
                            <p style="font-size: 16px; color: #555; line-height: 1.6; margin-bottom: 25px;">
                                Hello ${name}, we received a request to reset your password for your FireAlerts911 account.
                            </p>

                            <!-- Reset Button -->
                            <div style="text-align: center; margin: 35px 0;">
                                <a href="${resetUrl}"
                                   style="background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
                                          color: white;
                                          padding: 15px 30px;
                                          text-decoration: none;
                                          border-radius: 6px;
                                          display: inline-block;
                                          font-weight: bold;
                                          font-size: 16px;
                                          box-shadow: 0 3px 6px rgba(229, 57, 53, 0.3);
                                          transition: all 0.3s ease;">
                                    🔑 Reset My Password
                                </a>
                            </div>

                            <!-- Alternative Link -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; border-left: 4px solid #e53935; margin: 25px 0;">
                                <p style="margin: 0 0 10px 0; font-weight: bold; color: #333;">Can't click the button?</p>
                                <p style="margin: 0; font-size: 14px; color: #666; word-break: break-all;">
                                    Copy and paste this link into your browser: <br>
                                    <span style="color: #e53935; font-family: monospace;">${resetUrl}</span>
                                </p>
                            </div>

                            <!-- Security Notice -->
                            <div style="background: #fff3cd; padding: 15px; border-radius: 6px; border: 1px solid #ffeaa7; margin: 25px 0;">
                                <div style="display: flex; align-items: flex-start;">
                                    <span style="color: #856404; font-size: 18px; margin-right: 10px;">⚠️</span>
                                    <div>
                                        <strong style="color: #856404;">Security Notice:</strong>
                                        <ul style="margin: 5px 0 0 0; padding-left: 20px; color: #856404;">
                                            <li>This link expires in <strong>1 hour</strong> for your security</li>
                                            <li>If you didn't request this reset, please ignore this email</li>
                                            <li>Never share this link with anyone</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <p style="font-size: 14px; color: #666; line-height: 1.5; margin-top: 25px;">
                                If you have any questions or concerns, please contact our support team.
                            </p>
                        </div>

                        <!-- Footer -->
                        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
                            <p style="margin: 0; color: #666; font-size: 14px;">
                                © ${new Date().getFullYear()} FireAlerts911. All rights reserved.
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }    },

    // ==========================================
    // EMERGENCY & INCIDENT NOTIFICATIONS
    // ==========================================

    /**
     * Universal Incident Update Email Template
     * Gmail-compatible with table-based layouts and inline styles
     */
    incidentUpdate: {
        generate(user, incident, notification) {
            // Use the Gmail-compatible template
            const gmailTemplate = require('./gmailCompatibleEmailTemplate');
            return gmailTemplate.generateGmailCompatibleIncidentUpdate(user, incident, notification);
        },

        getTextContent(user, incident, notification) {
            const incidentType = incident?.incidentType?.name || 'Fire Incident';
            const location = `${incident.address}, ${incident.city}, ${incident.county}, ${incident.state}`;

            return `
INCIDENT UPDATE - ${incidentType.toUpperCase()}

Hello ${user.firstName || user.email},

This is an update for the ${incidentType} at ${location}.

INCIDENT DETAILS:
- Type: ${incidentType}
- Location: ${location}
- Status: ${incident.status?.name || 'Active'}
- Date/Time: ${incident.incidentDate ? new Date(incident.incidentDate).toLocaleString() : 'Not specified'}
- Severity: ${incident.severity || 'Moderate'}

${incident.description ? `DESCRIPTION:\n${incident.description}\n` : ''}

${incident.incidentDetail && (incident.incidentDetail.propertyOwner || incident.incidentDetail.dwellingType || incident.incidentDetail.yearBuilt || incident.incidentDetail.propertyValue) ? `PROPERTY INFORMATION:
${incident.incidentDetail.propertyOwner ? `- Owner: ${incident.incidentDetail.propertyOwner}\n` : ''}${incident.incidentDetail.dwellingType ? `- Type: ${incident.incidentDetail.dwellingType}\n` : ''}${incident.incidentDetail.yearBuilt ? `- Year Built: ${incident.incidentDetail.yearBuilt}\n` : ''}${incident.incidentDetail.propertyValue ? `- Property Value: ${new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(incident.incidentDetail.propertyValue)}\n` : ''}
` : ''}

${incident.incidentDetail ? this.getIncidentDetailsText(incident.incidentDetail, incidentType) : ''}

This incident information has been updated. Please stay alert and follow local emergency guidance.

For more information, visit: ${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/view-incident.html?id=${incident.id}

To unsubscribe from these alerts, visit: ${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/unsubscribe

Stay safe,
FireAlerts911 Team
            `.trim();
        },

        getIncidentDetailsText(details, incidentType) {
            const isFireIncident = incidentType.toLowerCase().includes('fire');
            const isWaterIncident = incidentType.toLowerCase().includes('water');

            let text = '\nINCIDENT DETAILS:\n';

            if (isFireIncident) {
                if (details.fireType) text += `- Fire Type: ${details.fireType}\n`;
                if (details.structureType) text += `- Structure Type: ${details.structureType}\n`;
                if (details.fireSize) text += `- Fire Size: ${details.fireSize}\n`;
                if (details.containmentStatus) text += `- Containment: ${details.containmentStatus}\n`;
                if (details.estimatedDamage) text += `- Estimated Damage: ${details.estimatedDamage}\n`;
                if (details.injuries) text += `- Injuries: ${details.injuries}\n`;
                if (details.fatalities) text += `- Fatalities: ${details.fatalities}\n`;
                if (details.evacuations) text += `- Evacuations: ${details.evacuations ? 'Yes' : 'No'}\n`;
                if (details.roadClosures) text += `- Road Closures: ${details.roadClosures ? 'Yes' : 'No'}\n`;
                if (details.cause) text += `- Cause: ${details.cause}\n`;
                if (details.resourcesDeployed) text += `- Resources: ${details.resourcesDeployed}\n`;
                if (details.weatherConditions) text += `- Weather: ${details.weatherConditions}\n`;
            } else if (isWaterIncident) {
                if (details.waterType) text += `- Water Issue Type: ${details.waterType}\n`;
                if (details.affectedCustomers) text += `- Affected Customers: ${details.affectedCustomers}\n`;
                if (details.estimatedRepairTime) text += `- Estimated Repair Time: ${details.estimatedRepairTime}\n`;
                if (details.waterPressure) text += `- Water Pressure: ${details.waterPressure}\n`;
                if (details.serviceDisruption) text += `- Service Disruption: ${details.serviceDisruption ? 'Yes' : 'No'}\n`;
                if (details.boilWaterAdvisory) text += `- Boil Water Advisory: ${details.boilWaterAdvisory ? 'Yes' : 'No'}\n`;
                if (details.alternativeSupply) text += `- Alternative Supply: ${details.alternativeSupply ? 'Yes' : 'No'}\n`;
                if (details.cause) text += `- Cause: ${details.cause}\n`;
                if (details.repairCrew) text += `- Repair Crew: ${details.repairCrew}\n`;
                if (details.priority) text += `- Priority: ${details.priority}\n`;
            } else {
                // Generic incident details
                if (details.cause) text += `- Cause: ${details.cause}\n`;
                if (details.estimatedDamage) text += `- Estimated Damage: ${details.estimatedDamage}\n`;
                if (details.injuries) text += `- Injuries: ${details.injuries}\n`;
                if (details.evacuations) text += `- Evacuations: ${details.evacuations ? 'Yes' : 'No'}\n`;
                if (details.roadClosures) text += `- Road Closures: ${details.roadClosures ? 'Yes' : 'No'}\n`;
            }

            return text;
        },

        getHtmlContent(user, incident, notification) {
            const incidentType = incident?.incidentType?.name || 'Incident';
            const location = `${incident.address}, ${incident.city}, ${incident.county}, ${incident.state}`;
            const userName = user.firstName || user.email;
            const frontendUrl = process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com';

            // Dynamic styling based on incident type
            const isFireIncident = incidentType.toLowerCase().includes('fire');
            const isWaterIncident = incidentType.toLowerCase().includes('water');

            let primaryColor = '#e53935'; // Fire red
            let secondaryColor = '#d32f2f';
            let emoji = '🚨';
            let gradientColors = '#e53935 0%, #d32f2f 100%';

            if (isWaterIncident) {
                primaryColor = '#1e88e5'; // Water blue
                secondaryColor = '#1976d2';
                emoji = '💧';
                gradientColors = '#1e88e5 0%, #1976d2 100%';
            } else if (isFireIncident) {
                emoji = '🔥';
            }

            return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - ${incidentType} Update</title>
    <style>
        /* FireAlerts911 Enhanced Email Styles - Universal Template */
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: #f5f5f5;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            width: 100%;
            background-color: #1a2035 !important;
        }

        /* Gmail-specific background fix */
        table.gmail-background {
            background-color: #1a2035 !important;
            width: 100% !important;
            min-height: 100vh !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        td.email-wrapper {
            background-color: #1a2035 !important;
            padding: 20px !important;
            width: 100% !important;
            vertical-align: top !important;
        }

        /* Force background colors in Gmail */
        .email-container {
            background-color: #272e48 !important;
        }

        .header {
            background: linear-gradient(135deg, ${gradientColors}) !important;
        }

        .content {
            background-color: #272e48 !important;
        }

        .footer {
            background-color: #1a2035 !important;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #272e48;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
            border: 1px solid #464d63;
        }

        .header {
            background: linear-gradient(135deg, ${gradientColors});
            padding: 30px 25px;
            text-align: center;
            position: relative;
            color: white;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 14px;
            opacity: 0.95;
            text-transform: uppercase;
            letter-spacing: 1.2px;
        }

        .update-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-top: 15px;
            font-size: 14px;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="fire" patternUnits="userSpaceOnUse" width="20" height="20"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23fire)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            color: white;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .tagline {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
        }

        .alert-badge {
            display: inline-block;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .content {
            padding: 30px;
        }

        .greeting {
            font-size: 18px;
            color: #f5f5f5;
            margin-bottom: 20px;
        }

        .update-notice {
            background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 6px;
            margin-bottom: 25px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 143, 0, 0.3);
        }

        .section {
            margin-bottom: 25px;
            background-color: #1a2035;
            border-radius: 6px;
            padding: 20px;
            border-left: 4px solid #e53935;
        }

        .section-title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 600;
            color: #e53935;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .section-icon {
            margin-right: 10px;
            font-size: 18px;
        }

        .info-grid {
            display: table;
            width: 100%;
        }

        .info-row {
            display: table-row;
        }

        .info-label {
            display: table-cell;
            padding: 8px 15px 8px 0;
            font-weight: 600;
            color: #b8c2cc;
            width: 30%;
            vertical-align: top;
        }

        .info-value {
            display: table-cell;
            padding: 8px 0;
            color: #f5f5f5;
            vertical-align: top;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active { background-color: #28a745; color: white; }
        .status-pending { background-color: #ff8f00; color: white; }
        .status-critical { background-color: #e53935; color: white; }

        .fire-details {
            background-color: rgba(229, 57, 53, 0.1);
            border: 1px solid rgba(229, 57, 53, 0.3);
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e88e5 0%, #1976d2 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(30, 136, 229, 0.3);
        }

        .btn-secondary {
            background-color: transparent;
            color: #b8c2cc;
            border: 1px solid #464d63;
        }

        .footer {
            background-color: #1a2035;
            padding: 25px;
            text-align: center;
            border-top: 1px solid #464d63;
        }

        .footer-content {
            color: #b8c2cc;
            font-size: 14px;
            line-height: 1.5;
        }

        .footer-links {
            margin: 15px 0;
        }

        .footer-links a {
            color: #1e88e5;
            text-decoration: none;
            margin: 0 10px;
        }

        .copyright {
            font-size: 12px;
            color: #666;
            margin-top: 15px;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            body { padding: 10px; }
            .content { padding: 20px; }
            .btn { display: block; margin: 10px 0; }
        }
    </style>
</head>
<body>
    <!-- Gmail Background Fix -->
    <table class="gmail-background" cellpadding="0" cellspacing="0" border="0" width="100%">
        <tr>
            <td class="email-wrapper">
                <div class="email-container">
                    <!-- Header -->
                    <div class="header">
                        <div class="header-content">
                            <div class="logo">🔥 FireAlerts911</div>
                            <div class="tagline">Emergency Notification System</div>
                            <div class="alert-badge">Fire Incident Update</div>
                        </div>
                    </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello ${userName},</div>

            <div class="update-notice">
                🔄 This is an update for the ${incidentType} incident at ${location}
            </div>

            <!-- Incident Information -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">🚨</span>
                    Incident Details
                </div>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">Type:</div>
                        <div class="info-value">${incidentType}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Location:</div>
                        <div class="info-value">${location}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Status:</div>
                        <div class="info-value">
                            <span class="status-badge status-${(incident.status?.name || 'active').toLowerCase()}">${incident.status?.name || 'Active'}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Date/Time:</div>
                        <div class="info-value">${incident.incidentDate ? new Date(incident.incidentDate).toLocaleString() : 'Not specified'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Severity:</div>
                        <div class="info-value">${incident.severity || 'Moderate'}</div>
                    </div>
                </div>

                ${incident.incidentDetail ? this.getFireDetailsHtml(incident.incidentDetail) : ''}
            </div>

            ${incident.description ? `
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📋</span>
                    Description
                </div>
                <div style="color: #f5f5f5; line-height: 1.6;">
                    ${incident.description.replace(/\n/g, '<br>')}
                </div>
            </div>
            ` : ''}

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/view-incident.html?id=${incident.id}" class="btn btn-primary">
                    View Full Details
                </a>
                <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/unsubscribe" class="btn btn-secondary">
                    Unsubscribe
                </a>
            </div>

            <div style="background-color: #1a2035; padding: 15px; border-radius: 6px; border-left: 4px solid #ff8f00; color: #b8c2cc; font-size: 14px;">
                <strong>⚠️ Safety Notice:</strong> This incident information has been updated. Please stay alert and follow local emergency guidance.
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <div><strong>FireAlerts911</strong> - Keeping Communities Safe</div>
                <div>Emergency Notification System</div>

                <div class="footer-links">
                    <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}">Dashboard</a>
                    <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/alerts">View Alerts</a>
                    <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/settings">Settings</a>
                </div>

                <div class="copyright">
                    © ${new Date().getFullYear()} FireAlerts911. All rights reserved.
                </div>
            </div>
        </div>
    </div>
            </td>
        </tr>
    </table>
</body>
</html>
            `;
        },

        getFireDetailsHtml(details) {
            if (!details) return '';

            let html = '<div class="fire-details">';
            html += '<div style="font-weight: 600; color: #e53935; margin-bottom: 10px;">🔥 Fire Incident Details</div>';
            html += '<div class="info-grid">';

            if (details.fireSize) {
                html += `<div class="info-row"><div class="info-label">Fire Size:</div><div class="info-value">${details.fireSize}</div></div>`;
            }
            if (details.containmentStatus) {
                html += `<div class="info-row"><div class="info-label">Containment:</div><div class="info-value">${details.containmentStatus}</div></div>`;
            }
            if (details.estimatedDamage) {
                html += `<div class="info-row"><div class="info-label">Est. Damage:</div><div class="info-value">${details.estimatedDamage}</div></div>`;
            }
            if (details.injuries) {
                html += `<div class="info-row"><div class="info-label">Injuries:</div><div class="info-value">${details.injuries}</div></div>`;
            }
            if (details.fatalities) {
                html += `<div class="info-row"><div class="info-label">Fatalities:</div><div class="info-value">${details.fatalities}</div></div>`;
            }
            if (details.evacuations !== undefined) {
                html += `<div class="info-row"><div class="info-label">Evacuations:</div><div class="info-value">${details.evacuations ? 'Yes' : 'No'}</div></div>`;
            }
            if (details.roadClosures !== undefined) {
                html += `<div class="info-row"><div class="info-label">Road Closures:</div><div class="info-value">${details.roadClosures ? 'Yes' : 'No'}</div></div>`;
            }
            if (details.resourcesDeployed) {
                html += `<div class="info-row"><div class="info-label">Resources:</div><div class="info-value">${details.resourcesDeployed}</div></div>`;
            }
            if (details.weatherConditions) {
                html += `<div class="info-row"><div class="info-label">Weather:</div><div class="info-value">${details.weatherConditions}</div></div>`;
            }

            html += '</div></div>';
            return html;
        }
    },

    /**
     * Incident Alert Email Template (Gmail-Compatible)
     * For new incident notifications
     */
    incident: {
        generate(user, incident, notification) {
            // Use the Gmail-compatible template for new incidents
            const gmailTemplate = require('./gmailCompatibleEmailTemplate');
            return gmailTemplate.generateGmailCompatibleNewIncident(user, incident, notification);
        },

        // Text and HTML content now handled by Gmail-compatible template

        getHtmlContent(user, incident, notification) {
            const incidentType = incident?.incidentType?.name || 'Incident';
            const location = `${incident.address}, ${incident.city}, ${incident.county}, ${incident.state}`;
            const severity = incident.severity || 'moderate';

            // Create Google Maps link
            const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location)}`;

            // Create incident details link
            const baseUrl = process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com';
            const incidentDetailsUrl = `${baseUrl}/view-incident.html?id=${incident.id}`;

            // Create unsubscribe link
            const unsubscribeUrl = `${baseUrl}/notifications.html`;

            return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 Emergency Notification</title>
    <style>
        /* Reset styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* Base styles */
        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #1a2035 0%, #272e48 100%);
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .alert-badge {
            background: #e53935;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }

        /* Content */
        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .info-grid {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e53935;
        }

        .info-row {
            display: flex;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: bold;
            color: #555;
            width: 120px;
            flex-shrink: 0;
        }

        .info-value {
            color: #333;
            flex: 1;
        }

        .btn {
            background: #e53935;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            font-weight: bold;
            margin: 5px 10px 5px 0;
        }

        /* Footer */
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
        }

        .footer-text {
            margin-bottom: 15px;
        }

        .footer-links {
            margin-bottom: 15px;
        }

        .footer-links a {
            color: #e53935;
            text-decoration: none;
            margin: 0 5px;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }
            .content {
                padding: 15px !important;
            }
            .info-label {
                display: block !important;
                width: auto !important;
                padding-bottom: 5px !important;
            }
            .info-value {
                display: block !important;
                padding-top: 0 !important;
                margin-bottom: 15px !important;
            }
            .btn {
                display: block !important;
                margin: 10px 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚨 FireAlerts911</div>
            <div>Emergency Notification System</div>
            <div class="alert-badge">🚨 ${severity.toUpperCase()} ALERT</div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Incident Information -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">🚨</span>
                    ${incidentType} Alert
                </div>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">Type:</div>
                        <div class="info-value">${incidentType}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Location:</div>
                        <div class="info-value">${location}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Date/Time:</div>
                        <div class="info-value">${incident.incidentDate ? new Date(incident.incidentDate).toLocaleString() : 'Not specified'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Status:</div>
                        <div class="info-value">${incident.status || 'Active'}</div>
                    </div>
                </div>
            </div>

            ${notification.content ? `
            <!-- Additional Information -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📝</span>
                    Additional Information
                </div>
                <p style="color: #666; line-height: 1.6;">${notification.content}</p>
            </div>
            ` : ''}

            <!-- Action Buttons -->
            <div class="section" style="text-align: center;">
                <a href="${mapsUrl}" target="_blank" class="btn">📍 View on Map</a>
                <a href="${incidentDetailsUrl}" target="_blank" class="btn">🔍 View Details</a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                This alert was sent to <strong>${user.email}</strong> because you are subscribed to emergency notifications for <strong>${incident.county} County, ${incident.state}</strong>.
            </div>
            <div class="footer-links">
                <a href="${unsubscribeUrl}" target="_blank">Manage Notifications</a> |
                <a href="${incidentDetailsUrl}" target="_blank">View Incident</a> |
                <a href="mailto:<EMAIL>">Contact Support</a>
            </div>
            <div style="margin-top: 15px; font-size: 11px; color: #999;">
                FireAlerts911 - Keeping Communities Safe<br>
                Emergency Notification System
            </div>
            <div style="margin-top: 10px;">© ${new Date().getFullYear()} FireAlerts911. All rights reserved.</div>
        </div>
    </div>
</body>
</html>
            `;
        }    },

    // ==========================================
    // SYSTEM & ADMINISTRATIVE EMAILS
    // ==========================================

    /**
     * Test Email Template (Gmail-Compatible)
     */
    test: {
        generate() {
            // Use the Gmail-compatible test template
            const gmailTemplate = require('./gmailCompatibleEmailTemplate');
            return gmailTemplate.generateGmailCompatibleTestEmail();
        },

        // Text and HTML content now handled by Gmail-compatible template
    },

    /**
     * Bulk Notification Email Template
     */
    bulk: {
        generate(subject, message, includeMap = false) {
            return {
                subject: subject,
                text: this.getTextContent(subject, message),
                html: this.getHtmlContent(subject, message, includeMap)
            };
        },

        getTextContent(subject, message) {
            return `
${subject}

${message}

This message was sent by FireAlerts911 administration.

FireAlerts911 Team
            `.trim();
        },

        getHtmlContent(subject, message, includeMap) {
            return `
                <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #e53935; margin: 0;">FireAlerts911</h1>
                        <p style="color: #666; margin: 5px 0 0 0;">System Notification</p>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #e53935;">
                        <h2 style="color: #333; margin-top: 0;">${subject}</h2>
                        <div style="color: #333; line-height: 1.6;">
                            ${message.replace(/\n/g, '<br>')}
                        </div>
                    </div>

                    ${includeMap ? `
                    <div style="margin: 30px 0; text-align: center;">
                        <p style="color: #666; margin-bottom: 15px;">View incident locations on our map:</p>
                        <a href="${process.env.FRONTEND_URL || 'https://dispatchsite.onrender.com'}/map"
                           style="background: #e53935; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                            View Map
                        </a>
                    </div>
                    ` : ''}

                    <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
                        <p>This message was sent by FireAlerts911 administration.</p>
                        <p style="margin: 0;">© ${new Date().getFullYear()} FireAlerts911. All rights reserved.</p>
                    </div>
                </div>
            `;
        }
    }
};

module.exports = emailTemplates;
