<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="robots" content="noindex,nofollow">
    <meta name="application-name" content="FireAlerts911">
    <title>FireAlerts911 - Forgot Password</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Override auth container styles to match login exactly */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: var(--primary-dark);
            box-sizing: border-box;
            padding: 0;
        }

        /* Make sure auth card has exact same dimensions */
        .auth-card {
            width: 400px;
            background-color: var(--secondary-dark);
            border-radius: 5px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin: 0 auto;
        }

        /* Notification styling */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
            max-width: 350px;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
            transform: translateX(120%);
            transition: transform 0.3s ease-in-out;
            z-index: 1000;
            display: flex;
            align-items: center;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background-color: #4caf50;
        }

        .notification.error {
            background-color: #f44336;
        }

        .notification.info {
            background-color: #2196f3;
        }

        .notification.warning {
            background-color: #ff9800;
        }

        .notification i {
            margin-right: 10px;
        }

        /* Form validation styling */
        .form-control.error {
            border-color: #f44336;
        }

        .error-message {
            color: #f44336;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        /* Success message styling */
        .success-message {
            color: #4caf50;
            font-size: 14px;
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4caf50;
            display: none;
        }

        .success-message.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <i class="fas fa-fire-alt fa-2x" style="color: #e53935; margin-right: 10px;"></i>
                    <h1 style="margin: 0; font-size: 24px;">FireAlerts911</h1>
                </div>
                <h2 style="margin: 0; font-size: 18px; font-weight: 400;">Reset Password</h2>
                <p style="margin: 10px 0 0 0; font-size: 14px; color: var(--text-secondary);">Enter your email address and we'll send you a link to reset your password.</p>
            </div>
            <div class="auth-body">
                <form id="forgotPasswordForm" action="javascript:void(0);" method="post" autocomplete="on">
                    <div class="form-group">
                        <label class="form-label" for="email">Email Address</label>
                        <div style="position: relative;">
                            <input type="email" class="form-control" id="email" name="email" required autocomplete="email" style="padding-left: 40px;" placeholder="Enter your email address">
                            <i class="fas fa-envelope" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: var(--text-secondary);"></i>
                        </div>
                        <div class="error-message" id="email-error">Please enter a valid email address</div>
                    </div>

                    <!-- Success Message Container -->
                    <div class="success-message" id="success-message">
                        <i class="fas fa-check-circle"></i> Password reset link sent! Check your email for instructions.
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" id="resetButton" style="width: 100%;">
                            <i class="fas fa-paper-plane"></i> Send Reset Link
                        </button>
                    </div>
                </form>
            </div>
            <div class="auth-footer">
                <a href="login.html" style="color: var(--accent-blue); text-decoration: none;">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            </div>
        </div>
    </div>
    
    <!-- Notification element -->
    <div class="notification" id="notification">
        <i class="fas fa-info-circle"></i>
        <span id="notification-message"></span>
    </div>
    
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Local notification function
            function showLocalNotification(message, type = "info") {
                const notification = document.getElementById("notification");
                const notificationMessage = document.getElementById("notification-message");

                // Remove existing classes
                notification.className = "notification";
                notification.classList.add(type);

                // Set message
                notificationMessage.textContent = message;

                // Show notification
                notification.classList.add("show");

                // Hide after 5 seconds
                setTimeout(() => {
                    notification.classList.remove("show");
                }, 5000);
            }

            // Make function available globally for this page
            window.showLocalNotification = showLocalNotification;

            // Form validation function
            function validateForm() {
                const email = document.getElementById("email");
                let isValid = true;

                // Validate email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!email.value.trim() || !emailRegex.test(email.value.trim())) {
                    email.classList.add("error");
                    document.getElementById("email-error").classList.add("show");
                    isValid = false;
                } else {
                    email.classList.remove("error");
                    document.getElementById("email-error").classList.remove("show");
                }

                return isValid;
            }

            // Add input event listener to remove error state when typing
            document.getElementById("email").addEventListener("input", function() {
                this.classList.remove("error");
                document.getElementById("email-error").classList.remove("show");
                document.getElementById("success-message").classList.remove("show");
            });

            // Form submission handler
            document.getElementById("forgotPasswordForm").addEventListener("submit", function(e) {
                e.preventDefault();

                // Validate form
                if (!validateForm()) {
                    return;
                }

                // Hide any previous success messages
                document.getElementById("success-message").classList.remove("show");

                const email = this.elements.email.value.trim();

                // Show loading state
                const submitButton = document.getElementById("resetButton");
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Sending...";
                submitButton.disabled = true;

                // Call the API forgot password method
                API.auth.forgotPassword(email)
                    .then(response => {
                        console.log("Forgot password response:", response);

                        if (response && response.success !== false) {
                            // Show success message in form
                            document.getElementById("success-message").classList.add("show");

                            // Also show notification
                            if (typeof window.showNotification === "function") {
                                window.showNotification("Check your email for reset instructions", "success");
                            } else {
                                showLocalNotification("Check your email for reset instructions", "success");
                            }

                            // Clear the form
                            this.reset();

                            // Redirect to login after a delay
                            setTimeout(() => {
                                window.location.href = "login.html?message=reset_sent";
                            }, 4000);
                        } else {
                            // Show error message
                            const errorMessage = response?.message || "Failed to send reset link. Please try again.";
                            if (typeof window.showNotification === "function") {
                                window.showNotification(errorMessage, "error");
                            } else {
                                showLocalNotification(errorMessage, "error");
                            }
                        }
                    })
                    .catch(error => {
                        console.error("Forgot password error:", error);

                        // Show error notification
                        if (typeof window.showNotification === "function") {
                            window.showNotification("An error occurred while connecting to the server. Please try again.", "error");
                        } else {
                            showLocalNotification("An error occurred while connecting to the server. Please try again.", "error");
                        }
                    })
                    .finally(() => {
                        // Reset button state
                        submitButton.innerHTML = originalText;
                        submitButton.disabled = false;
                    });
            });
        });
    </script>
</body>
</html>
