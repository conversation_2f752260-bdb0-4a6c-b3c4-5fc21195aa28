["G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "G:\\GitHub\\faApp\\test_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]